<?php

namespace Modules\Rajapicker\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Spatie\Image\Image;
use Illuminate\Support\Str;
use Spatie\Image\Enums\Fit;
use Spatie\Image\Enums\ImageDriver as SpatieImageDriver;

class RajaPickerThumbnailService
{
    protected RajaPickerConfigService $configService;

    public function __construct()
    {
        $this->configService = new RajaPickerConfigService();
    }

    /**
     * Generate thumbnail untuk file tertentu
     */
    public function generateThumbnail(string $originalPath, string $sizeName = 'small'): ?string
    {
        try {
            // Cek apakah thumbnail diaktifkan
            if (!$this->configService->isThumbnailEnabled()) {
                return null;
            }

            // Cek apakah file asli ada
            if (!Storage::disk($this->configService->getStorageDisk())->exists($originalPath)) {
                Log::warning("File asli tidak ditemukan: {$originalPath}");
                return null;
            }

            // Dapatkan konfigurasi ukuran thumbnail
            $sizeConfig = $this->configService->getThumbnailSizeConfig($sizeName);
            if (!$sizeConfig) {
                Log::warning("Konfigurasi ukuran thumbnail tidak ditemukan: {$sizeName}");
                return null;
            }

            // Generate path thumbnail
            $thumbnailPath = $this->configService->generateThumbnailPath($originalPath, $sizeName);
            
            // Cek apakah thumbnail sudah ada
            if (Storage::disk($this->configService->getStorageDisk())->exists($thumbnailPath)) {
                return $thumbnailPath;
            }

            // Buat thumbnail
            $this->createThumbnail($originalPath, $thumbnailPath, $sizeConfig);

            // Generate WebP version jika diaktifkan
            if ($this->configService->isThumbnailWebpEnabled()) {
                $this->createWebpThumbnail($originalPath, $sizeName, $sizeConfig);
            }

            return $thumbnailPath;

        } catch (\Exception $e) {
            Log::error("Error generating thumbnail: " . $e->getMessage(), [
                'original_path' => $originalPath,
                'size_name' => $sizeName,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Generate semua thumbnail untuk file tertentu
     */
    public function generateAllThumbnails(string $originalPath): array
    {
        $results = [];
        $sizeNames = $this->configService->getThumbnailSizeNames();

        foreach ($sizeNames as $sizeName) {
            $thumbnailPath = $this->generateThumbnail($originalPath, $sizeName);
            if ($thumbnailPath) {
                $results[$sizeName] = $thumbnailPath;
            }
        }

        return $results;
    }

    /**
     * Buat thumbnail dengan ukuran tertentu
     */
    protected function createThumbnail(string $originalPath, string $thumbnailPath, array $sizeConfig): void
    {
        $disk = $this->configService->getStorageDisk();
        $originalFullPath = Storage::disk($disk)->path($originalPath);
        $thumbnailFullPath = Storage::disk($disk)->path($thumbnailPath);

        // Pastikan direktori thumbnail ada
        $thumbnailDir = dirname($thumbnailFullPath);
        if (!is_dir($thumbnailDir)) {
            mkdir($thumbnailDir, 0755, true);
        }

        // Pastikan file sumber ada secara fisik
        if (!file_exists($originalFullPath)) {
            Log::warning("File tidak ditemukan di path fisik: {$originalFullPath}");
            return;
        }

        $quality = $sizeConfig['quality'] ?? 85;

        // Hitung target ukuran
        if (isset($sizeConfig['percentage'])) {
            $tempImg = Image::useImageDriver('gd')->loadFile($originalFullPath);
            $origW = $tempImg->getWidth();
            $origH = $tempImg->getHeight();
            $targetW = (int) round($origW * ($sizeConfig['percentage'] / 100));
            $targetH = (int) round($origH * ($sizeConfig['percentage'] / 100));
        } else {
            $targetW = $sizeConfig['width'] ?? null;
            $targetH = $sizeConfig['height'] ?? null;
        }

        // Paksa gunakan driver GD untuk menghindari error ImageMagick
        Image::useImageDriver('gd')
            ->loadFile($originalFullPath)
            ->fit(Fit::Crop, $targetW, $targetH)
            ->quality($quality)
            ->save($thumbnailFullPath);

        // Log::info("Thumbnail berhasil dibuat: {$thumbnailPath}");
    }

    /**
     * Buat thumbnail WebP
     */
    protected function createWebpThumbnail(string $originalPath, string $sizeName, array $sizeConfig): ?string
    {
        try {
            $disk = $this->configService->getStorageDisk();
            $originalFullPath = Storage::disk($disk)->path($originalPath);

            // Pastikan file sumber ada secara fisik
            if (!file_exists($originalFullPath)) {
                Log::warning("File tidak ditemukan di path fisik: {$originalFullPath}");
                return null;
            }

            $pathInfo = pathinfo($originalPath);
            $thumbnailName = $this->configService->generateThumbnailName($pathInfo['basename'], $sizeName);
            $thumbnailNameWithoutExt = pathinfo($thumbnailName, PATHINFO_FILENAME);
            $webpThumbnailName = $thumbnailNameWithoutExt . '.webp';

            // Perbaikan: Gunakan generateThumbnailPath untuk mendapatkan path lengkap yang benar
            $regularThumbnailPath = $this->configService->generateThumbnailPath($originalPath, $sizeName);
            $webpThumbnailPath = dirname($regularThumbnailPath) . '/' . $webpThumbnailName;
            $webpThumbnailFullPath = Storage::disk($disk)->path($webpThumbnailPath);

            if (Storage::disk($disk)->exists($webpThumbnailPath)) {
                return $webpThumbnailPath;
            }

            if (!is_dir(dirname($webpThumbnailFullPath))) {
                mkdir(dirname($webpThumbnailFullPath), 0755, true);
            }

            $quality = $sizeConfig['quality'] ?? 85;

            // Hitung target ukuran
            if (isset($sizeConfig['percentage'])) {
                $tempImg = Image::useImageDriver('gd')->loadFile($originalFullPath);
                $origW = $tempImg->getWidth();
                $origH = $tempImg->getHeight();
                $targetW = (int) round($origW * ($sizeConfig['percentage'] / 100));
                $targetH = (int) round($origH * ($sizeConfig['percentage'] / 100));
            } else {
                $targetW = $sizeConfig['width'] ?? null;
                $targetH = $sizeConfig['height'] ?? null;
            }

            // Paksa gunakan driver GD untuk menghindari error ImageMagick
            Image::useImageDriver('gd')
                ->loadFile($originalFullPath)
                ->fit(Fit::Crop, $targetW, $targetH)
                ->format('webp')
                ->quality($quality)
                ->save($webpThumbnailFullPath);

            // Log::info("WebP thumbnail berhasil dibuat: {$webpThumbnailPath}");
            return $webpThumbnailPath;

        } catch (\Exception $e) {
            Log::error("Error creating WebP thumbnail: " . $e->getMessage(), [
                'original_path' => $originalPath,
                'size_name' => $sizeName
            ]);
            return null;
        }
    }

    /**
     * Hapus thumbnail untuk file tertentu
     */
    public function deleteThumbnails(string $originalPath): bool
    {
        try {
            $disk = $this->configService->getStorageDisk();
            $thumbnailPaths = $this->configService->getAllThumbnailPaths($originalPath);
            $deletedCount = 0;

            foreach ($thumbnailPaths as $sizeName => $thumbnailPath) {
                if (Storage::disk($disk)->exists($thumbnailPath)) {
                    Storage::disk($disk)->delete($thumbnailPath);
                    $deletedCount++;
                }

                // Hapus WebP version jika ada
                $webpPath = $this->getWebpThumbnailPath($thumbnailPath);
                if (Storage::disk($disk)->exists($webpPath)) {
                    Storage::disk($disk)->delete($webpPath);
                    $deletedCount++;
                }
            }

            Log::info("Berhasil menghapus {$deletedCount} thumbnail untuk: {$originalPath}");
            return true;

        } catch (\Exception $e) {
            Log::error("Error deleting thumbnails: " . $e->getMessage(), [
                'original_path' => $originalPath
            ]);
            return false;
        }
    }

    /**
     * Dapatkan path WebP thumbnail
     */
    protected function getWebpThumbnailPath(string $thumbnailPath): string
    {
        $pathInfo = pathinfo($thumbnailPath);
        $webpPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . '.webp';
        
        // Pastikan path menggunakan forward slash
        $webpPath = str_replace('\\', '/', $webpPath);
        
        return $webpPath;
    }

    /**
     * Regenerate thumbnail (hapus yang lama dan buat yang baru)
     */
    public function regenerateThumbnail(string $originalPath, string $sizeName = 'small'): ?string
    {
        // Hapus thumbnail yang ada
        $thumbnailPath = $this->configService->generateThumbnailPath($originalPath, $sizeName);
        $disk = $this->configService->getStorageDisk();
        
        if (Storage::disk($disk)->exists($thumbnailPath)) {
            Storage::disk($disk)->delete($thumbnailPath);
        }

        // Hapus WebP version jika ada
        $webpPath = $this->getWebpThumbnailPath($thumbnailPath);
        if (Storage::disk($disk)->exists($webpPath)) {
            Storage::disk($disk)->delete($webpPath);
        }

        // Buat thumbnail baru
        return $this->generateThumbnail($originalPath, $sizeName);
    }

    /**
     * Regenerate semua thumbnail
     */
    public function regenerateAllThumbnails(string $originalPath): array
    {
        // Hapus semua thumbnail yang ada
        $this->deleteThumbnails($originalPath);

        // Buat thumbnail baru
        return $this->generateAllThumbnails($originalPath);
    }

    /**
     * Cek apakah thumbnail ada
     */
    public function thumbnailExists(string $originalPath, string $sizeName = 'small'): bool
    {
        $thumbnailPath = $this->configService->generateThumbnailPath($originalPath, $sizeName);
        return Storage::disk($this->configService->getStorageDisk())->exists($thumbnailPath);
    }

    /**
     * Dapatkan URL thumbnail
     */
    public function getThumbnailUrl(string $originalPath, string $sizeName = 'small'): ?string
    {
        if ($this->thumbnailExists($originalPath, $sizeName)) {
            return $this->configService->generateThumbnailUrl($originalPath, $sizeName);
        }

        // Jika thumbnail tidak ada, generate dulu
        $thumbnailPath = $this->generateThumbnail($originalPath, $sizeName);
        if ($thumbnailPath) {
            return $this->configService->generateThumbnailUrl($originalPath, $sizeName);
        }

        return null;
    }

    /**
     * Dapatkan semua URL thumbnail
     */
    public function getAllThumbnailUrls(string $originalPath): array
    {
        $urls = [];
        $sizeNames = $this->configService->getThumbnailSizeNames();

        foreach ($sizeNames as $sizeName) {
            $url = $this->getThumbnailUrl($originalPath, $sizeName);
            if ($url) {
                $urls[$sizeName] = $url;
            }
        }

        return $urls;
    }

    /**
     * Batch generate thumbnail untuk multiple files
     */
    public function batchGenerateThumbnails(array $filePaths, string $sizeName = 'small'): array
    {
        $results = [];
        
        foreach ($filePaths as $filePath) {
            $thumbnailPath = $this->generateThumbnail($filePath, $sizeName);
            $results[$filePath] = $thumbnailPath;
        }

        return $results;
    }

    /**
     * Clean up thumbnail yang tidak terpakai
     */
    public function cleanupOrphanedThumbnails(): int
    {
        $disk = $this->configService->getStorageDisk();
        $thumbnailDir = $this->configService->getThumbnailDirectory();
        $deletedCount = 0;

        try {
            $thumbnailFiles = Storage::disk($disk)->allFiles($thumbnailDir);

            foreach ($thumbnailFiles as $thumbnailFile) {
                // Extract original filename dari nama thumbnail
                $originalFileName = $this->extractOriginalFileName($thumbnailFile);
                
                if ($originalFileName) {
                    // Cek apakah file asli masih ada
                    $originalPath = $this->findOriginalFile($originalFileName);
                    
                    if (!$originalPath || !Storage::disk($disk)->exists($originalPath)) {
                        // File asli tidak ada, hapus thumbnail
                        Storage::disk($disk)->delete($thumbnailFile);
                        $deletedCount++;
                    }
                }
            }

            Log::info("Berhasil menghapus {$deletedCount} thumbnail yang tidak terpakai");
            return $deletedCount;

        } catch (\Exception $e) {
            Log::error("Error cleaning up orphaned thumbnails: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Extract nama file asli dari nama thumbnail
     */
    protected function extractOriginalFileName(string $thumbnailPath): ?string
    {
        $prefix = $this->configService->getThumbnailPrefix();
        $pathInfo = pathinfo($thumbnailPath);
        $filename = $pathInfo['filename'];

        // Remove prefix dan suffix
        $prefixPattern = str_replace('{name}', '(.+)', preg_quote($prefix, '/'));
        $suffixPattern = '_(small|medium|large)$';
        
        $pattern = "/^{$prefixPattern}{$suffixPattern}$/";
        
        if (preg_match($pattern, $filename, $matches)) {
            return $matches[1] . '.' . $pathInfo['extension'];
        }

        return null;
    }

    /**
     * Cari file asli berdasarkan nama file
     */
    protected function findOriginalFile(string $fileName): ?string
    {
        $disk = $this->configService->getStorageDisk();
        $collections = $this->configService->getAllCollections();

        foreach ($collections as $collectionName => $collectionConfig) {
            $directory = $collectionConfig['directory'] ?? '';
            $searchPath = $directory ? $directory . '/' . $fileName : $fileName;
            
            if (Storage::disk($disk)->exists($searchPath)) {
                return $searchPath;
            }
        }

        return null;
    }
} 